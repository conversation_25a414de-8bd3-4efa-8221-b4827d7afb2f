// 测试配置文件路径的脚本
const path = require('path');
const fs = require('fs');

console.log('=== 配置文件路径测试 ===');
console.log('当前工作目录:', process.cwd());
console.log('执行路径:', process.execPath);
console.log('资源路径:', process.resourcesPath || 'undefined');
console.log('__dirname:', __dirname);

// 测试不同的配置文件路径
const paths = [
    path.join(process.cwd(), 'config.json'),
    path.join(__dirname, 'config.json'),
    process.resourcesPath ? path.join(process.resourcesPath, 'config.json') : null,
    process.resourcesPath ? path.join(path.dirname(process.execPath), '..', 'Resources', 'config.json') : null
].filter(Boolean);

console.log('\n=== 测试路径 ===');
paths.forEach((testPath, index) => {
    console.log(`${index + 1}. ${testPath}`);
    console.log(`   存在: ${fs.existsSync(testPath)}`);
    if (fs.existsSync(testPath)) {
        try {
            const content = fs.readFileSync(testPath, 'utf8');
            const config = JSON.parse(content);
            console.log(`   富途配置: ${config.market?.host}:${config.market?.port}`);
        } catch (e) {
            console.log(`   读取失败: ${e.message}`);
        }
    }
    console.log('');
});
