import { ipcMain, IpcMainInvokeEvent, Menu } from 'electron';
import { WindowManager } from '../windowManager';
import { initializeTradingSystemService, getTradingSystemService, TradingSystemService } from '../services/TradingSystemService';
import { TaskCreateOptions } from '../trading/task-types';
import { configManager, AppConfig } from '../utils/configManager';
import { createContextMenu } from '../menu';

// API 响应类型
interface ApiResponse<T = any> {
    success: boolean;
    message: string;
    data: T | null;
    error?: string;
}

// 全局交易系统服务实例
let tradingSystemService: TradingSystemService | null = null;

// 错误处理装饰器
function handleError(target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value;
    
    descriptor.value = async function (...args: any[]) {
        try {
            return await method.apply(this, args);
        } catch (error) {
            console.error(`Error in ${propertyName}:`, error);
            return {
                success: false,
                message: `操作失败: ${error instanceof Error ? error.message : '未知错误'}`,
                data: null,
                error: error instanceof Error ? error.stack : String(error)
            };
        }
    };
}

/**
 * 初始化交易系统服务
 */
function initializeTradingSystem(): void {
    if (tradingSystemService) {
        return;
    }

    try {
        // 初始化交易系统服务
        tradingSystemService = initializeTradingSystemService({
            environment: (process.env.NODE_ENV as any) || 'development',
            enableAutoStart: true
        });

        // 监听交易系统服务事件
        tradingSystemService.on('initialized', () => {
            console.log('[IPC] 交易系统服务初始化完成');
            broadcastEvent('trading-system:service-ready', { timestamp: Date.now() });
        });

        tradingSystemService.on('shutdown', () => {
            console.log('[IPC] 交易系统服务已关闭');
            broadcastEvent('trading-system:service-shutdown', { timestamp: Date.now() });
        });

        // 异步初始化交易系统
        tradingSystemService.initialize().catch(error => {
            console.error('[IPC] 交易系统初始化失败:', error);
            broadcastEvent('trading-system:initialization-error', {
                error: error.message,
                timestamp: Date.now()
            });
        });

    } catch (error) {
        console.error('[IPC] 创建交易系统服务失败:', error);
    }
}

/**
 * 注册所有 IPC 处理器
 */
export function registerIPCHandlers(): void {
    // 初始化交易系统服务
    initializeTradingSystem();

    // 注册交易系统相关处理器
    registerTradingSystemHandlers();

    // 注册任务管理处理器
    registerTaskHandlers();

    // 注册配置管理处理器
    registerConfigHandlers();

    // 注册华盛通交易处理器
    registerHuashengHandlers();

    // 注册窗口控制处理器
    registerWindowHandlers();

    // 注册右键菜单处理器
    registerContextMenuHandlers();

    // 注册编辑操作处理器
    registerEditHandlers();

    console.log('All IPC handlers registered');
}

/**
 * 注册编辑操作处理器
 */
function registerEditHandlers(): void {
    // 复制
    ipcMain.handle('edit:copy', async (event) => {
        const webContents = event.sender;
        webContents.copy();
        return true;
    });

    // 剪切
    ipcMain.handle('edit:cut', async (event) => {
        const webContents = event.sender;
        webContents.cut();
        return true;
    });

    // 粘贴
    ipcMain.handle('edit:paste', async (event) => {
        const webContents = event.sender;
        webContents.paste();
        return true;
    });

    // 全选
    ipcMain.handle('edit:selectAll', async (event) => {
        const webContents = event.sender;
        webContents.selectAll();
        return true;
    });

    // 撤销
    ipcMain.handle('edit:undo', async (event) => {
        const webContents = event.sender;
        webContents.undo();
        return true;
    });

    // 重做
    ipcMain.handle('edit:redo', async (event) => {
        const webContents = event.sender;
        webContents.redo();
        return true;
    });
}

/**
 * 注册交易系统相关处理器
 */
function registerTradingSystemHandlers(): void {
    // 获取交易系统状态
    ipcMain.handle('trading-system:getStatus', async (event) => {
        try {
            const service = getTradingSystemService();
            if (!service) {
                return createResponse(false, '交易系统服务未初始化', null);
            }

            const status = service.getStatus();
            return createResponse(true, '获取状态成功', status);
        } catch (error) {
            return createResponse(false, `获取状态失败: ${error}`, null);
        }
    });

    // 初始化交易系统
    ipcMain.handle('trading-system:initialize', async (event, args) => {
        try {
            const service = getTradingSystemService();
            if (!service) {
                return createResponse(false, '交易系统服务未初始化', null);
            }

            if (service.isReady()) {
                return createResponse(true, '交易系统已初始化', null);
            }

            await service.initialize();
            return createResponse(true, '交易系统初始化成功', null);
        } catch (error) {
            return createResponse(false, `初始化失败: ${error}`, null);
        }
    });

    // 关闭交易系统
    ipcMain.handle('trading-system:shutdown', async (event) => {
        try {
            const service = getTradingSystemService();
            if (!service) {
                return createResponse(false, '交易系统服务未初始化', null);
            }

            await service.shutdown();
            return createResponse(true, '交易系统关闭成功', null);
        } catch (error) {
            return createResponse(false, `关闭失败: ${error}`, null);
        }
    });

    // 获取行情连接状态
    ipcMain.handle('trading-system:getMarketStatus', async (event) => {
        try {
            const service = getTradingSystemService();
            if (!service) {
                return createResponse(false, '交易系统服务未初始化', null);
            }

            const tradingSystem = service.getTradingSystem();
            if (!tradingSystem) {
                return createResponse(false, '交易系统实例不存在', null);
            }

            // 直接获取行情状态，不依赖整体系统就绪状态
            try {
                const marketStats = tradingSystem.getMarketDataStats();
                return createResponse(true, '获取行情状态成功', marketStats);
            } catch (error) {
                console.warn('[IPC] 获取行情统计失败，返回基础状态:', error);
                // 即使获取统计失败，也尝试返回基础连接状态
                const isConnected = (tradingSystem as any).marketDataManager?.isAdapterConnected() || false;
                return createResponse(true, '获取行情基础状态成功', {
                    isConnected,
                    message: '行情数据管理器状态'
                });
            }
        } catch (error) {
            return createResponse(false, `获取行情状态失败: ${error}`, null);
        }
    });

    // 获取交易连接状态
    ipcMain.handle('trading-system:getTradingStatus', async (event) => {
        try {
            const service = getTradingSystemService();
            if (!service || !service.isReady()) {
                return createResponse(false, '交易系统未就绪', null);
            }

            const tradingSystem = service.getTradingSystem();
            if (!tradingSystem) {
                return createResponse(false, '交易系统实例不存在', null);
            }

            const status = tradingSystem.getTradingStatus();
            
            return createResponse(true, '获取交易状态成功', status);
        } catch (error) {
            return createResponse(false, `获取交易状态失败: ${error}`, null);
        }
    });
}

/**
 * 注册任务管理处理器
 */
function registerTaskHandlers(): void {
    // 创建任务
    ipcMain.handle('task:create', async (event, taskConfig: TaskCreateOptions) => {
        try {
            const service = getTradingSystemService();
            if (!service) {
                return createResponse(false, '交易系统服务未初始化', null);
            }

            // 只要服务已初始化就可以获取任务列表，不需要等待所有适配器连接
            if (!service.isReady()) {
                // 如果系统未完全就绪，返回空列表而不是错误
                console.log('[IPC] 交易系统未完全就绪，返回空任务列表');
                return createResponse(true, '系统初始化中，任务列表为空', []);
            }

            const taskId = await service.createTask(taskConfig);
            return createResponse(true, '任务创建成功', { taskId });
        } catch (error) {
            return createResponse(false, `创建任务失败: ${error}`, null);
        }
    });

    // 启动任务
    ipcMain.handle('task:start', async (event, args) => {
        try {
            const { taskId } = args;
            const service = getTradingSystemService();
            if (!service || !service.isReady()) {
                return createResponse(false, '交易系统未就绪', null);
            }

            await service.startTask(taskId);
            return createResponse(true, '任务启动成功', { taskId });
        } catch (error) {
            return createResponse(false, `启动任务失败: ${error}`, null);
        }
    });

    // 停止任务
    ipcMain.handle('task:stop', async (event, args) => {
        try {
            const { taskId } = args;
            const service = getTradingSystemService();
            if (!service || !service.isReady()) {
                return createResponse(false, '交易系统未就绪', null);
            }

            await service.stopTask(taskId);
            return createResponse(true, '任务停止成功', { taskId });
        } catch (error) {
            return createResponse(false, `停止任务失败: ${error}`, null);
        }
    });

    // 获取任务列表
    ipcMain.handle('task:getList', async (event) => {
        try {
            const service = getTradingSystemService();
            if (!service || !service.isReady()) {
                return createResponse(false, '交易系统未就绪', null);
            }

            const taskList = service.getTaskList();
            return createResponse(true, '获取任务列表成功', taskList);
        } catch (error) {
            return createResponse(false, `获取任务列表失败: ${error}`, null);
        }
    });

    // 获取任务状态
    ipcMain.handle('task:getStatus', async (event, args) => {
        try {
            const { taskId } = args;
            const service = getTradingSystemService();
            if (!service || !service.isReady()) {
                return createResponse(false, '交易系统未就绪', null);
            }

            const status = service.getTaskStatus(taskId);
            if (status === null) {
                return createResponse(false, '任务不存在', null);
            }

            return createResponse(true, '获取任务状态成功', { taskId, status });
        } catch (error) {
            return createResponse(false, `获取任务状态失败: ${error}`, null);
        }
    });

    // 删除任务
    ipcMain.handle('task:delete', async (event, args) => {
        try {
            const { taskId } = args;
            const service = getTradingSystemService();
            if (!service || !service.isReady()) {
                return createResponse(false, '交易系统未就绪', null);
            }

            const tradingSystem = service.getTradingSystem();
            if (!tradingSystem) {
                return createResponse(false, '交易系统实例不存在', null);
            }

            // 先停止任务再删除
            await tradingSystem.stopTask(taskId);
            await tradingSystem.deleteTask(taskId);
            
            return createResponse(true, '任务删除成功', { taskId });
        } catch (error) {
            return createResponse(false, `删除任务失败: ${error}`, null);
        }
    });
}

/**
 * 注册配置管理处理器
 */
function registerConfigHandlers(): void {
    // 获取完整配置
    ipcMain.handle('config:getAll', async (event) => {
        try {
            const config = configManager.getConfig();
            return createResponse(true, '获取配置成功', config);
        } catch (error) {
            return createResponse(false, `获取配置失败: ${error}`, null);
        }
    });

    // 获取特定配置部分
    ipcMain.handle('config:get', async (event, args) => {
        try {
            const { section } = args;
            const config = configManager.getConfig();
            
            if (!section) {
                return createResponse(true, '获取配置成功', config);
            }
            
            const sectionData = (config as any)[section];
            if (sectionData === undefined) {
                return createResponse(false, `配置节不存在: ${section}`, null);
            }
            
            return createResponse(true, '获取配置成功', sectionData);
        } catch (error) {
            return createResponse(false, `获取配置失败: ${error}`, null);
        }
    });

    // 保存配置
    ipcMain.handle('config:save', async (event, args) => {
        try {
            const { config } = args;
            await configManager.saveConfig(config as AppConfig);
            return createResponse(true, '配置保存成功', null);
        } catch (error) {
            return createResponse(false, `配置保存失败: ${error}`, null);
        }
    });

    // 更新配置的特定部分
    ipcMain.handle('config:update', async (event, args) => {
        try {
            const { updates } = args;
            const newConfig = await configManager.updateConfig(updates);
            return createResponse(true, '配置更新成功', newConfig);
        } catch (error) {
            return createResponse(false, `配置更新失败: ${error}`, null);
        }
    });

    // 重新加载配置
    ipcMain.handle('config:reload', async (event) => {
        try {
            const config = configManager.reloadConfig();

            // 重新加载配置后，需要重启交易系统以应用新配置
            const service = getTradingSystemService();
            if (service && service.isReady()) {
                console.log('[ConfigManager] 配置重新加载，准备重启交易系统...');
                // 注意：这里不直接重启，而是发送事件让用户决定
                // 因为重启可能会中断正在运行的任务
            }

            return createResponse(true, '配置重新加载成功', config);
        } catch (error) {
            return createResponse(false, `配置重新加载失败: ${error}`, null);
        }
    });

    // 验证配置
    ipcMain.handle('config:validate', async (event, args) => {
        try {
            const { config } = args;
            const isValid = configManager['validateConfig'](config);
            return createResponse(true, '配置验证完成', { isValid });
        } catch (error) {
            return createResponse(false, `配置验证失败: ${error}`, { isValid: false });
        }
    });
}

/**
 * 注册窗口控制处理器
 */
function registerWindowHandlers(): void {
    ipcMain.on('window:minimize', () => {
        WindowManager.minimize();
    });

    ipcMain.on('window:maximize', () => {
        WindowManager.toggleMaximize();
    });

    ipcMain.on('window:close', () => {
        WindowManager.close();
    });

    ipcMain.on('window:toggleDevTools', () => {
        WindowManager.toggleDevTools();
    });

    ipcMain.on('window:reload', () => {
        WindowManager.reload();
    });
}

/**
 * 创建标准响应
 */
function createResponse<T>(success: boolean, message: string, data: T): ApiResponse<T> {
    return {
        success,
        message,
        data
    };
}

/**
 * 发送事件到渲染进程
 */
export function sendToRenderer(channel: string, ...args: any[]): void {
    WindowManager.send(channel, ...args);
}

/**
 * 广播事件到渲染进程
 */
function broadcastEvent(eventType: string, data: any): void {
    try {
        sendToRenderer(eventType, data);
    } catch (error) {
        console.error('[IPC] 广播事件失败:', eventType, error);
    }
}

/**
 * 广播实时数据
 */
export function broadcastRealtimeData(adapter: string, data: any): void {
    sendToRenderer('event:realtimeData', {
        adapter,
        data,
        timestamp: Date.now()
    });
}

/**
 * 注册华盛通交易处理器
 */
function registerHuashengHandlers(): void {
    // 获取账户信息
    ipcMain.handle('huasheng:getAccountInfo', async (event) => {
        try {
            const service = getTradingSystemService();
            if (!service) {
                return createResponse(false, '交易系统服务未初始化', null);
            }

            const tradingSystem = service.getTradingSystem();
            if (!tradingSystem) {
                return createResponse(false, '交易系统实例不存在', null);
            }

            // 获取华盛通适配器
            const tradingManager = (tradingSystem as any).tradingManager;
            if (!tradingManager) {
                return createResponse(false, '交易管理器未初始化', null);
            }

            const huashengAdapter = tradingManager.getAdapter('huasheng');
            if (!huashengAdapter) {
                return createResponse(false, '华盛通适配器未初始化', null);
            }

            const accountInfo = await huashengAdapter.getAccountInfo();
            return createResponse(true, '获取账户信息成功', accountInfo);
        } catch (error) {
            return createResponse(false, `获取账户信息失败: ${error}`, null);
        }
    });

    // 获取资金信息
    ipcMain.handle('huasheng:getFunds', async (event) => {
        try {
            const service = getTradingSystemService();
            if (!service) {
                return createResponse(false, '交易系统服务未初始化', null);
            }

            const tradingSystem = service.getTradingSystem();
            if (!tradingSystem) {
                return createResponse(false, '交易系统实例不存在', null);
            }

            // 获取华盛通适配器
            const tradingManager = (tradingSystem as any).tradingManager;
            if (!tradingManager) {
                return createResponse(false, '交易管理器未初始化', null);
            }

            const huashengAdapter = tradingManager.getAdapter('huasheng');
            if (!huashengAdapter) {
                return createResponse(false, '华盛通适配器未初始化', null);
            }

            const funds = await huashengAdapter.getFunds();
            return createResponse(true, '获取资金信息成功', funds);
        } catch (error) {
            return createResponse(false, `获取资金信息失败: ${error}`, null);
        }
    });

    // 连接华盛通
    ipcMain.handle('huasheng:connect', async (event) => {
        try {
            const service = getTradingSystemService();
            if (!service) {
                return createResponse(false, '交易系统服务未初始化', null);
            }

            const tradingSystem = service.getTradingSystem();
            if (!tradingSystem) {
                return createResponse(false, '交易系统实例不存在', null);
            }

            // 获取华盛通适配器
            const tradingManager = (tradingSystem as any).tradingManager;
            if (!tradingManager) {
                return createResponse(false, '交易管理器未初始化', null);
            }

            await tradingManager.connect('huasheng');
            return createResponse(true, '华盛通连接成功', null);
        } catch (error) {
            return createResponse(false, `华盛通连接失败: ${error}`, null);
        }
    });

    // 断开华盛通连接
    ipcMain.handle('huasheng:disconnect', async (event) => {
        try {
            const service = getTradingSystemService();
            if (!service) {
                return createResponse(false, '交易系统服务未初始化', null);
            }

            const tradingSystem = service.getTradingSystem();
            if (!tradingSystem) {
                return createResponse(false, '交易系统实例不存在', null);
            }

            // 获取华盛通适配器
            const tradingManager = (tradingSystem as any).tradingManager;
            if (!tradingManager) {
                return createResponse(false, '交易管理器未初始化', null);
            }

            await tradingManager.disconnect('huasheng');
            return createResponse(true, '华盛通断开连接成功', null);
        } catch (error) {
            return createResponse(false, `华盛通断开连接失败: ${error}`, null);
        }
    });

    // 获取华盛通连接状态
    ipcMain.handle('huasheng:getConnectionStatus', async (event) => {
        try {
            const service = getTradingSystemService();
            if (!service) {
                return createResponse(false, '交易系统服务未初始化', null);
            }

            const tradingSystem = service.getTradingSystem();
            if (!tradingSystem) {
                return createResponse(false, '交易系统实例不存在', null);
            }

            // 获取华盛通适配器
            const tradingManager = (tradingSystem as any).tradingManager;
            if (!tradingManager) {
                return createResponse(false, '交易管理器未初始化', null);
            }

            const huashengAdapter = tradingManager.getAdapter('huasheng');
            if (!huashengAdapter) {
                return createResponse(false, '华盛通适配器未初始化', null);
            }

            const status = {
                isConnected: huashengAdapter.isConnected(),
                isLoggedIn: huashengAdapter.isLoggedIn ? huashengAdapter.isLoggedIn() : false
            };
            return createResponse(true, '获取华盛通连接状态成功', status);
        } catch (error) {
            return createResponse(false, `获取华盛通连接状态失败: ${error}`, null);
        }
    });
}

/**
 * 广播连接状态
 */
export function broadcastConnectionStatus(adapter: string, status: string): void {
    sendToRenderer('event:connectionStatus', {
        adapter,
        status,
        timestamp: Date.now()
    });
}

/**
 * 广播错误信息
 */
export function broadcastError(adapter: string, error: string): void {
    sendToRenderer('event:error', {
        adapter,
        error,
        timestamp: Date.now()
    });
}

/**
 * 注册右键菜单处理器
 */
function registerContextMenuHandlers(): void {
    ipcMain.on('context-menu:show', (event) => {
        const contextMenu = createContextMenu();
        const window = WindowManager.getMainWindow();
        if (window) {
            contextMenu.popup({ window });
        }
    });
}