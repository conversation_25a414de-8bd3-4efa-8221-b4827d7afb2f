/**
 * 配置管理器
 * 统一管理应用配置的读取、保存和验证
 */

import * as fs from 'fs';
import * as path from 'path';

// 配置接口定义
export interface AppConfig {
    app: {
        name: string;
        version: string;
        environment: 'development' | 'production' | 'test';
    };
    market: {
        provider: string;
        host: string;
        port: number;
        encryptKey: string;
        useHttps: boolean;
    };
    trading: {
        huasheng: {
            host: string;
            port: number;
            account: string;
            password: string;
            enabled: boolean;
            heartbeatInterval: number;
            reconnectInterval: number;
            maxReconnectAttempts: number;
            commandTimeout: number;
        };
    };
    system: {
        enablePersistence: boolean;
        logLevel: 'debug' | 'info' | 'warn' | 'error';
        taskExecutionInterval: number;
        riskCheckInterval: number;
    };
    logging: {
        level: string;
        maxFiles: number;
        maxSize: string;
    };
    riskControl: {
        maxDailyLoss: number;
        maxSingleOrderValue: number;
        enableEmergencyStop: boolean;
    };
}

export class ConfigManager {
    private static instance: ConfigManager;
    private config: AppConfig | null = null;
    private configPath: string;

    private constructor() {
        // 配置文件路径 - 支持开发和打包环境
        this.configPath = this.getConfigPath();
    }

    /**
     * 获取配置文件路径
     */
    private getConfigPath(): string {
        // 检查是否为打包后的应用
        const isPackaged = process.env.NODE_ENV === 'production' ||
                          process.resourcesPath ||
                          process.execPath.includes('.app/Contents/MacOS');

        if (isPackaged) {
            // 打包后的环境 - 尝试多个可能的路径
            const possiblePaths: string[] = [
                // 标准的 extraResources 路径
                process.resourcesPath ? path.join(process.resourcesPath, 'config.json') : '',
                // macOS 应用包路径
                path.join(path.dirname(process.execPath), '..', 'Resources', 'config.json'),
                // 备用路径
                path.join(path.dirname(process.execPath), 'config.json'),
                path.join(process.cwd(), 'config.json')
            ].filter(p => p.length > 0);

            // 返回第一个存在的配置文件路径
            for (const configPath of possiblePaths) {
                if (fs.existsSync(configPath)) {
                    return configPath;
                }
            }

            // 如果都不存在，返回默认路径
            return possiblePaths[0] || path.join(process.cwd(), 'config.json');
        } else {
            // 开发环境
            return path.join(process.cwd(), 'config.json');
        }
    }

    public static getInstance(): ConfigManager {
        if (!ConfigManager.instance) {
            ConfigManager.instance = new ConfigManager();
        }
        return ConfigManager.instance;
    }

    /**
     * 加载配置文件
     */
    public loadConfig(): AppConfig {
        try {
            if (this.config) {
                return this.config;
            }

            const configContent = fs.readFileSync(this.configPath, 'utf8');
            const config = JSON.parse(configContent) as AppConfig;

            // 环境变量覆盖敏感信息
            this.applyEnvironmentOverrides(config);

            // 验证配置
            this.validateConfig(config);

            this.config = config;
            console.log(`[ConfigManager] 配置加载成功: ${this.configPath}`);

            return config;
        } catch (error) {
            console.error('[ConfigManager] 配置加载失败:', error);
            throw new Error(`配置文件加载失败: ${error instanceof Error ? error.message : '未知错误'}`);
        }
    }

    /**
     * 保存配置文件
     */
    public async saveConfig(config: AppConfig): Promise<void> {
        try {
            const configJson = JSON.stringify(config, null, 2);
            fs.writeFileSync(this.configPath, configJson, 'utf8');
            
            // 更新内存中的配置
            this.config = config;
            
            console.log(`[ConfigManager] 配置保存成功: ${this.configPath}`);
        } catch (error) {
            console.error('[ConfigManager] 配置保存失败:', error);
            throw new Error(`配置文件保存失败: ${error instanceof Error ? error.message : '未知错误'}`);
        }
    }

    /**
     * 重新加载配置
     */
    public reloadConfig(): AppConfig {
        this.config = null;
        return this.loadConfig();
    }

    /**
     * 获取当前配置
     */
    public getConfig(): AppConfig {
        if (!this.config) {
            return this.loadConfig();
        }
        return this.config;
    }

    /**
     * 更新配置的特定部分
     */
    public async updateConfig(updates: Partial<AppConfig>): Promise<AppConfig> {
        const currentConfig = this.getConfig();
        const newConfig = this.deepMerge(currentConfig, updates);
        
        await this.saveConfig(newConfig);
        return newConfig;
    }

    /**
     * 应用环境变量覆盖
     */
    private applyEnvironmentOverrides(config: AppConfig): void {
        // 华盛通账户信息覆盖
        if (process.env.HUASHENG_HOST) {
            config.trading.huasheng.host = process.env.HUASHENG_HOST;
        }
        if (process.env.HUASHENG_PORT) {
            config.trading.huasheng.port = parseInt(process.env.HUASHENG_PORT, 10);
        }
        if (process.env.HUASHENG_ACCOUNT) {
            config.trading.huasheng.account = process.env.HUASHENG_ACCOUNT;
        }
        if (process.env.HUASHENG_PASSWORD) {
            config.trading.huasheng.password = process.env.HUASHENG_PASSWORD;
        }

        // 富途行情配置覆盖
        if (process.env.FUTU_HOST) {
            config.market.host = process.env.FUTU_HOST;
        }
        if (process.env.FUTU_PORT) {
            config.market.port = parseInt(process.env.FUTU_PORT, 10);
        }
        if (process.env.FUTU_ENCRYPT_KEY) {
            config.market.encryptKey = process.env.FUTU_ENCRYPT_KEY;
        }

        // 环境配置覆盖
        if (process.env.NODE_ENV) {
            config.app.environment = process.env.NODE_ENV as 'development' | 'production' | 'test';
        }
    }

    /**
     * 验证配置
     */
    private validateConfig(config: AppConfig): void {
        // 验证必要的配置项
        if (!config.app || !config.app.name) {
            throw new Error('缺少应用配置');
        }

        if (!config.market || !config.market.host || !config.market.port) {
            throw new Error('缺少行情配置');
        }

        if (!config.trading || !config.trading.huasheng) {
            throw new Error('缺少交易配置');
        }

        if (!config.system) {
            throw new Error('缺少系统配置');
        }

        // 验证端口范围
        if (config.market.port < 1 || config.market.port > 65535) {
            throw new Error('行情端口配置无效');
        }

        if (config.trading.huasheng.port < 1 || config.trading.huasheng.port > 65535) {
            throw new Error('华盛通端口配置无效');
        }

        // 验证华盛通账户信息
        if (!config.trading.huasheng.account || !config.trading.huasheng.password) {
            console.warn('[ConfigManager] 华盛通账户信息未配置，可能导致连接失败');
        }
    }

    /**
     * 深度合并对象
     */
    private deepMerge(target: any, source: any): any {
        const result = { ...target };
        
        for (const key in source) {
            if (Object.prototype.hasOwnProperty.call(source, key)) {
                if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
                    result[key] = this.deepMerge(target[key] || {}, source[key]);
                } else {
                    result[key] = source[key];
                }
            }
        }
        
        return result;
    }


}

// 导出单例实例和便捷函数
export const configManager = ConfigManager.getInstance();

export function getConfig(): AppConfig {
    return configManager.getConfig();
}

export function validateConfig(config: AppConfig): boolean {
    try {
        configManager['validateConfig'](config);
        return true;
    } catch (error) {
        console.error('配置验证失败:', error);
        return false;
    }
}