// electron-builder configuration

/**
 * @type {import('electron-builder').Configuration}
 */
module.exports = {
    appId: "com.quanttrading.terminal",
    productName: "量化交易终端",
    directories: {
        output: "dist",
        buildResources: "build"
    },
    files: [
        "dist-web/**/*",
        "electron-main/dist/**/*",
        "node_modules/**/*",
        "!node_modules/*/{CHANGELOG.md,README.md,README,readme.md,readme}",
        "!node_modules/*/{test,__tests__,tests,powered-test,example,examples}",
        "!node_modules/**/*.d.ts",
        "!**/.bin",
        "!**/{.DS_Store,.git,.hg,.svn,CVS,RCS,SCCS,.gitignore,.gitattributes}",
        "!**/{__pycache__,thumbs.db,.flowconfig,.idea,.vs,.nyc_output}",
        "!**/{appveyor.yml,.travis.yml,circle.yml}",
        "!**/{npm-debug.log,yarn.lock,.yarn-integrity,.yarn-metadata.json}"
    ],
    extraResources: [
        {
            from: "config.json",
            to: "config.json"
        }
    ],
    mac: {
        category: "public.app-category.finance",
        target: [
            {
                target: "dmg",
                arch: ["x64", "arm64"]
            },
            {
                target: "zip",
                arch: ["x64", "arm64"]
            }
        ],
        icon: "electron-main/assets/icons/icon.icns"
    },
    win: {
        target: [
            {
                target: "nsis",
                arch: ["x64"]
            },
            {
                target: "zip",
                arch: ["x64"]
            }
        ],
        icon: "electron-main/assets/icons/icon.ico"
    },
    linux: {
        target: ["AppImage", "deb"],
        category: "Finance",
        icon: "electron-main/assets/icons"
    },
    nsis: {
        oneClick: false,
        perMachine: false,
        allowToChangeInstallationDirectory: true,
        createDesktopShortcut: true,
        createStartMenuShortcut: true,
        deleteAppDataOnUninstall: false
    }
};
