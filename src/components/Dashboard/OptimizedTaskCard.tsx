import { memo, useMemo, useCallback } from 'react';
import { Task } from '@/types';
import { TaskCardErrorBoundary } from './ErrorBoundary';
import { PnLDisplay } from './PnLDisplay';
import { useTaskCardKeyboardNavigation } from './KeyboardInteractions';
import { ValueHighlight } from './AnimationSystem';
import { getStatusColor, getStatusText } from '@/styles/componentStyles';

interface OptimizedTaskCardProps {
    task: Task;
    onToggle: (taskId: string) => void;
    onShowDetails: (taskId: string) => void;
    onEdit: (taskId: string) => void;
    onDelete: (taskId: string) => void;
    onLiquidate: (taskId: string) => void;
}

// 性能优化的比较函数
const arePropsEqual = (prevProps: OptimizedTaskCardProps, nextProps: OptimizedTaskCardProps) => {
    const prevTask = prevProps.task;
    const nextTask = nextProps.task;
    
    // 只在关键数据变化时重新渲染
    return (
        prevTask.id === nextTask.id &&
        prevTask.status === nextTask.status &&
        prevTask.pnl === nextTask.pnl &&
        prevTask.position === nextTask.position &&
        prevTask.updatedAt === nextTask.updatedAt &&
        // 检查处理函数引用是否稳定
        prevProps.onToggle === nextProps.onToggle &&
        prevProps.onShowDetails === nextProps.onShowDetails &&
        prevProps.onEdit === nextProps.onEdit &&
        prevProps.onDelete === nextProps.onDelete &&
        prevProps.onLiquidate === nextProps.onLiquidate
    );
};

// 优化后的TaskCard组件
export const OptimizedTaskCard = memo<OptimizedTaskCardProps>(({ 
    task, 
    onToggle,
    onShowDetails,
    onEdit,
    onDelete,
    onLiquidate
}) => {
    // 使用useMemo缓存计算结果
    const pnlPercentage = useMemo(() => {
        if (!task.avgCost || task.position === 0) return 0;
        return (task.pnl / (task.avgCost * task.position)) * 100;
    }, [task.pnl, task.avgCost, task.position]);



    const runningDuration = useMemo(() => {
        const now = new Date();
        const created = new Date(task.createdAt);
        const hours = Math.floor((now.getTime() - created.getTime()) / (1000 * 60 * 60));
        
        if (hours < 24) {
            return `${hours}小时`;
        } else {
            const days = Math.floor(hours / 24);
            return `${days}天`;
        }
    }, [task.createdAt]);

    // 使用useCallback稳定事件处理函数
    const handleToggle = useCallback(() => onToggle(task.id), [task.id, onToggle]);
    const handleShowDetails = useCallback(() => onShowDetails(task.id), [task.id, onShowDetails]);
    const handleEdit = useCallback(() => onEdit(task.id), [task.id, onEdit]);
    const handleDelete = useCallback(() => onDelete(task.id), [task.id, onDelete]);
    const handleLiquidate = useCallback(() => onLiquidate(task.id), [task.id, onLiquidate]);

    // 键盘导航
    const { handleKeyDown } = useTaskCardKeyboardNavigation(task.id, {
        onToggle: handleToggle,
        onShowDetails: handleShowDetails,
        onEdit: handleEdit,
        onDelete: handleDelete,
        onLiquidate: handleLiquidate
    });

    // 获取卡片样式类
    const getCardClassName = useMemo(() => {
        const baseClasses = 'task-card';
        const statusClasses = `status-${task.status}`;
        return `${baseClasses} ${statusClasses}`;
    }, [task.status]);

    return (
        <TaskCardErrorBoundary taskId={task.id}>
            <div
                className={getCardClassName}
                tabIndex={0}
                role="article"
                aria-label={`${task.stockName} ${task.strategyName} 任务卡片`}
                onKeyDown={handleKeyDown}
                style={{
                    background: '#f8f9fa',
                    border: '1px solid #e9ecef',
                    borderRadius: '8px',
                    padding: '16px',
                    position: 'relative',
                    borderLeft: `4px solid ${getStatusBorderColor(task.status)}`,
                    transition: 'all 0.2s ease',
                    cursor: 'pointer'
                }}
            >
                {/* 任务头部信息 */}
                <div style={{ display: "flex", justifyContent: "space-between", alignItems: "flex-start", marginBottom: "12px" }}>
                    <div style={{ flex: 1, minWidth: 0 }}>
                        <h3 style={{ margin: 0, fontSize: "18px", fontWeight: "bold", color: "#495057", marginBottom: "2px" }}>
                            {task.stockName}
                        </h3>
                        <div style={{ margin: 0, fontSize: "14px", color: "#6c757d", fontWeight: "500", marginBottom: "4px" }}>
                            {task.stockCode}
                        </div>
                        <p style={{ margin: 0, fontSize: "13px", color: "#666", fontWeight: "500" }}>
                            {task.strategyName}
                        </p>
                    </div>
                    <div style={{
                        color: getStatusColor(task.status),
                        fontSize: "12px",
                        fontWeight: "500",
                        padding: "4px 8px",
                        borderRadius: "4px",
                        backgroundColor: `${getStatusColor(task.status)}20`
                    }}>
                        {getStatusText(task.status)}
                    </div>
                </div>

                {/* 关键指标展示 - 带数值变化高亮 */}
                <ValueHighlight value={task.pnl}>
                    <div style={{ background: "white", border: "1px solid #dee2e6", borderRadius: "6px", padding: "12px", marginTop: "8px" }}>
                        <PnLDisplay pnl={task.pnl} pnlPercentage={pnlPercentage} size="medium" showTrend={false} />

                        {/* 持仓信息 */}
                        <div style={{ display: "grid", gridTemplateColumns: "1fr 1fr", gap: "12px", marginTop: "12px" }}>
                            <div>
                                <div style={{ fontSize: "11px", color: "#6c757d", marginBottom: "4px" }}>持仓数量</div>
                                <div style={{ fontSize: "14px", fontWeight: "500", color: task.position > 0 ? "#28a745" : "#6c757d" }}>
                                    {task.position > 0 ? `${task.position.toLocaleString()} 股` : "空仓"}
                                </div>
                            </div>
                            <div>
                                <div style={{ fontSize: "11px", color: "#6c757d", marginBottom: "4px" }}>成本价</div>
                                <div style={{ fontSize: "14px", fontWeight: "500", color: task.avgCost ? "#fd7e14" : "#6c757d" }}>
                                    {task.avgCost ? new Intl.NumberFormat("zh-CN", { style: "currency", currency: "HKD" }).format(task.avgCost) : "-"}
                                </div>
                            </div>
                        </div>

                        {/* 运行时长 */}
                        <div style={{ fontSize: "12px", color: "#868e96", marginTop: "8px" }}>
                            <span>运行时长: </span>
                            <span style={{ fontWeight: "500" }}>{runningDuration}</span>
                        </div>
                    </div>
                </ValueHighlight>

                {/* 操作按钮组 */}
                <div style={{ display: "flex", justifyContent: "space-between", alignItems: "center", gap: "8px", paddingTop: "12px", borderTop: "1px solid #f0f0f0", marginTop: "12px" }}>
                    <div style={{ display: "flex", gap: "8px" }}>
                        <button
                            style={{
                                padding: "6px 12px",
                                fontSize: "14px",
                                backgroundColor: task.status === "running" ? "#ffc107" : "#28a745",
                                color: task.status === "running" ? "#212529" : "white",
                                border: "none",
                                borderRadius: "4px",
                                cursor: "pointer"
                            }}
                            onClick={handleToggle}
                        >
                            {task.status === "running" ? "暂停" : "启动"}
                        </button>
                        <button
                            style={{
                                padding: "6px 12px",
                                fontSize: "14px",
                                backgroundColor: "#17a2b8",
                                color: "white",
                                border: "none",
                                borderRadius: "4px",
                                cursor: "pointer"
                            }}
                            onClick={handleShowDetails}
                        >
                            详情
                        </button>
                    </div>
                </div>
            </div>
        </TaskCardErrorBoundary>
    );
}, arePropsEqual);

// 获取状态边框颜色
function getStatusBorderColor(status: string): string {
    const colors: Record<string, string> = {
        running: '#28a745',
        paused: '#ffc107',
        stopped: '#6c757d',
        error: '#dc3545',
        liquidated: '#007bff'
    };
    return colors[status] || '#6c757d';
}

// 注意：VirtualizedTaskList 需要 react-window 依赖，如果需要使用请先安装
// npm install react-window @types/react-window