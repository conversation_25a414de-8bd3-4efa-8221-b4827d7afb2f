// 交易演示逻辑 Hook - Electron 版本
import { useState, useEffect } from 'react';
import { electronTradingClient, ConnectionStatus } from '@/communication';

export const useTradingDemo = () => {
    const [electronConnected, setElectronConnected] = useState(false);
    const [loading, setLoading] = useState(false);
    const [systemStatus, setSystemStatus] = useState<any>(null);
    const [realtimeData, setRealtimeData] = useState<any[]>([]);

    // 新增状态
    const [futuConnected, setFutuConnected] = useState(false);
    const [huashengConnected, setHuashengConnected] = useState(false);
    const [quote, setQuote] = useState<any>(null);
    const [funds, setFunds] = useState<any>(null);

    useEffect(() => {
        // 设置实时数据监听
        const setupRealtimeListeners = async () => {
            // 监听 Electron 实时数据
            electronTradingClient.onRealtimeData((event) => {
                console.log('Electron 实时数据:', event);
                setRealtimeData(prev => [...prev.slice(-9), event]); // 保留最新10条
            });

            electronTradingClient.onConnectionStatus((data) => {
                console.log('连接状态变化:', data);
                setElectronConnected(data.status === ConnectionStatus.Connected);
            });
        };

        setupRealtimeListeners();

        // 清理函数
        return () => {
            electronTradingClient.destroy();
        };
    }, []);

    // 获取系统状态
    const getSystemStatus = async () => {
        setLoading(true);
        try {
            const result = await electronTradingClient.getTradingSystemStatus();
            if (result.success) {
                setSystemStatus(result.data);
                setElectronConnected(result.data?.initialized || false);
                console.log('系统状态:', result.data);
            } else {
                console.error('获取系统状态失败:', result.message);
            }
        } catch (error) {
            console.error('获取系统状态异常:', error);
        } finally {
            setLoading(false);
        }
    };

    // 初始化系统
    const initializeSystem = async () => {
        setLoading(true);
        try {
            const result = await electronTradingClient.initializeTradingSystem();
            if (result.success) {
                setElectronConnected(true);
                console.log('系统初始化成功');
                await getSystemStatus(); // 刷新状态
            } else {
                console.error('系统初始化失败:', result.message);
            }
        } catch (error) {
            console.error('系统初始化异常:', error);
        } finally {
            setLoading(false);
        }
    };

    // 获取任务列表
    const getTaskList = async () => {
        if (!electronConnected) return;
        
        setLoading(true);
        try {
            const result = await electronTradingClient.getTaskList();
            if (result.success) {
                console.log('任务列表:', result.data);
                return result.data;
            }
        } catch (error) {
            console.error('获取任务列表失败:', error);
        } finally {
            setLoading(false);
        }
    };

    // 演示创建任务
    const createDemoTask = async () => {
        if (!electronConnected) return;
        
        try {
            const demoTaskConfig = {
                name: '演示任务',
                stockCode: 'HK.00700',
                stockName: '腾讯控股',
                market: 'HK' as any,
                strategyConfig: {
                    strategyType: 'strategy_a_big_order_monitor' as any,
                    params: {
                        monitorThreshold: 1000,
                        durationSeconds: 60,
                        targetBrokers: ['goldman_sachs'],
                        orderSize: 100
                    },
                    requiredDataTypes: []
                },
                riskConfig: {
                    triggerLogic: 'any' as any,
                    conditions: [],
                    liquidationStrategy: {
                        type: 'market' as any,
                        params: {}
                    }
                },
                autoStart: false
            };
            
            const result = await electronTradingClient.createTask(demoTaskConfig);
            console.log('创建演示任务结果:', result);
            return result;
        } catch (error) {
            console.error('创建演示任务失败:', error);
        }
    };

    // 新增方法 - 适配器控制
    const startFutuAdapter = async () => {
        setLoading(true);
        try {
            // 这里可以调用实际的富途适配器启动逻辑
            console.log('启动富途适配器...');
            // 模拟启动成功
            setFutuConnected(true);
        } catch (error) {
            console.error('启动富途适配器失败:', error);
        } finally {
            setLoading(false);
        }
    };

    const startHuashengAdapter = async () => {
        setLoading(true);
        try {
            console.log('启动华盛适配器...');
            setHuashengConnected(true);
        } catch (error) {
            console.error('启动华盛适配器失败:', error);
        } finally {
            setLoading(false);
        }
    };

    const connectFutu = async () => {
        setLoading(true);
        try {
            console.log('连接富途...');
            setFutuConnected(true);
        } catch (error) {
            console.error('连接富途失败:', error);
        } finally {
            setLoading(false);
        }
    };

    const connectHuasheng = async () => {
        setLoading(true);
        try {
            console.log('连接华盛...');
            setHuashengConnected(true);
        } catch (error) {
            console.error('连接华盛失败:', error);
        } finally {
            setLoading(false);
        }
    };

    // 数据查询方法
    const getFutuQuote = async () => {
        if (!futuConnected) return;
        setLoading(true);
        try {
            console.log('获取富途行情...');
            // 模拟行情数据
            setQuote({
                symbol: 'HK.00700',
                price: 320.5,
                change: 2.5,
                changePercent: 0.78,
                volume: 1234567,
                timestamp: new Date().toISOString()
            });
        } catch (error) {
            console.error('获取富途行情失败:', error);
        } finally {
            setLoading(false);
        }
    };

    const getHuashengFunds = async () => {
        if (!huashengConnected) return;
        setLoading(true);
        try {
            console.log('获取华盛资金...');
            // 模拟资金数据
            setFunds({
                totalAssets: 1000000,
                availableCash: 500000,
                marketValue: 500000,
                todayPnl: 12500,
                totalPnl: 125000
            });
        } catch (error) {
            console.error('获取华盛资金失败:', error);
        } finally {
            setLoading(false);
        }
    };

    const subscribeFutuRealtime = async () => {
        if (!futuConnected) return;
        try {
            console.log('订阅富途实时数据...');
            // 模拟实时数据推送
            const mockData = {
                type: 'quote',
                symbol: 'HK.00700',
                price: 320.5 + Math.random() * 2 - 1,
                timestamp: new Date().toISOString()
            };
            setRealtimeData(prev => [...prev.slice(-9), mockData]);
        } catch (error) {
            console.error('订阅富途实时数据失败:', error);
        }
    };

    const placeFutuOrder = async () => {
        if (!futuConnected) return;
        setLoading(true);
        try {
            console.log('下富途订单...');
            // 模拟下单成功
            const orderData = {
                type: 'order',
                orderId: 'ORDER_' + Date.now(),
                symbol: 'HK.00700',
                side: 'BUY',
                quantity: 100,
                price: 320.5,
                status: 'FILLED',
                timestamp: new Date().toISOString()
            };
            setRealtimeData(prev => [...prev.slice(-9), orderData]);
        } catch (error) {
            console.error('下富途订单失败:', error);
        } finally {
            setLoading(false);
        }
    };

    return {
        // 状态
        electronConnected,
        futuConnected,
        huashengConnected,
        loading,
        systemStatus,
        quote,
        funds,
        realtimeData,

        // 操作函数
        getSystemStatus,
        initializeSystem,
        getTaskList,
        createDemoTask,

        // 新增的适配器控制方法
        startFutuAdapter,
        startHuashengAdapter,
        connectFutu,
        connectHuasheng,
        getFutuQuote,
        getHuashengFunds,
        subscribeFutuRealtime,
        placeFutuOrder
    };
};
